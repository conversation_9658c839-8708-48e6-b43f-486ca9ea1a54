(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/WaterRippleEffect.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_three_build_three_module_90b4155f.js",
  "static/chunks/node_modules_@react-three_fiber_dist_dae08a07._.js",
  "static/chunks/node_modules_b1e08b4a._.js",
  "static/chunks/src_components_ui_WaterRippleEffect_tsx_1b78944c._.js",
  "static/chunks/src_components_ui_WaterRippleEffect_tsx_5ba01e8b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/WaterRippleEffect.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);