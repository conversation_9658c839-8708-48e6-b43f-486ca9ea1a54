{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden\">\r\n      <h1 className=\"text-black text-7xl flex items-center justify-center mt-100\">\r\n        I&apos;m\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-20 h-15 rounded-xl  mx-2 object-cover\"\r\n        />\r\n        <PERSON><PERSON> Chauhan\r\n      </h1>\r\n      <h2 className=\"text-black text-7xl flex items-center justify-center mt-4 font-bold\">\r\n        Bringing Ideas to reality\r\n      </h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAOA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBAA8D;kCAE1E,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;oBACV;;;;;;;0BAGJ,8OAAC;gBAAG,WAAU;0BAAsE;;;;;;;;;;;;AAK1F;uCAEe", "debugId": null}}]}