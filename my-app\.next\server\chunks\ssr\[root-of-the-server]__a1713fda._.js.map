{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden\">\r\n      <h1 className=\"text-black text-7xl flex items-center justify-center mt-100 \">\r\n        I&apos;m\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-20 h-15 rounded-xl  mx-2 object-cover\"\r\n        />\r\n        <PERSON><PERSON> Chauhan\r\n      </h1>\r\n      <h2 className=\"text-black text-7xl flex items-center justify-center mt-4  font-myfont\">\r\n        <PERSON><PERSON> Chauh<PERSON>\r\n      </h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAOA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBAA+D;kCAE3E,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;oBACV;;;;;;;0BAGJ,8OAAC;gBAAG,WAAU;0BAAyE;;;;;;;;;;;;AAK7F;uCAEe", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\n\nconst DURATION = 0.30;\nconst STAGGER = 0.025;\n\ninterface FlipLinkProps {\n  children: string;\n  href: string;\n}\n\nconst FlipLink: React.FC<FlipLinkProps> = ({ children, href }) => {\n  return (\n    <motion.a\n      initial=\"initial\"\n      whileHover=\"hovered\"\n      target=\"_blank\"\n      href={href}\n      className=\"relative block overflow-hidden whitespace-nowrap text-4xl font-semibold uppercase dark:text-white/90 sm:text-7xl md:text-8xl \"\n      style={{\n        lineHeight: 0.75,\n      }}\n    >\n      <div>\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: 0,\n              },\n              hovered: {\n                y: \"-100%\",\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n      <div className=\"absolute inset-0\">\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: \"100%\",\n              },\n              hovered: {\n                y: 0,\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n    </motion.a>\n  );\n};\n\nexport default FlipLink;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,WAAW;AACjB,MAAM,UAAU;AAOhB,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,SAAQ;QACR,YAAW;QACX,QAAO;QACP,MAAM;QACN,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,8OAAC;0BACE,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;0BAMX,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;;;;;;;AAQjB;uCAEe", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState, useRef, useCallback } from \"react\";\r\nimport FlipLink from \"./text-effect-flipper\";\r\nimport { useGSAP } from \"@gsap/react\";\r\nimport gsap from \"gsap\";\r\n\r\nconst MENU_ITEMS = [\r\n  { href: \"/\", label: \"Home\" },\r\n  { href: \"#about\", label: \"About\" },\r\n  { href: \"#projects\", label: \"Projects\" },\r\n  { href: \"#skills\", label: \"Skills\" }\r\n];\r\n\r\nconst MenuButton = ({ isOpen, onClick }) => (\r\n  <button \r\n    onClick={onClick}\r\n    className=\"text-4xl hover:text-gray-300 transition-colors\"\r\n    aria-label=\"Toggle menu\"\r\n  >\r\n    {isOpen ? '×' : '☰'}\r\n  </button>\r\n);\r\n\r\nconst MenuItem = ({ href, label, ref }) => (\r\n  <li ref={ref}>\r\n    <a className=\"text-4xl flex item-center hover:text-red-800 transition-colors transform inline-block ease-in\">\r\n      <FlipLink href={href}>{label}</FlipLink>\r\n    </a>\r\n  </li>\r\n);\r\n\r\nexport const Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const menuRef = useRef(null);\r\n  const menuItemsRef = useRef([]);\r\n\r\n  const handleMenuAnimation = useCallback(() => {\r\n    if (isMenuOpen) {\r\n      gsap.fromTo(menuRef.current,\r\n        { opacity: 0 },\r\n        { opacity: 1, duration: 0.5, ease: \"power2.inOut\" }\r\n      );\r\n\r\n      gsap.fromTo(menuItemsRef.current,\r\n        { y: -50, opacity: 0 },\r\n        {\r\n          y: 0,\r\n          opacity: 1,\r\n          duration: 0.6,\r\n          stagger: 0.1,\r\n          ease: \"back.out(1.7)\"\r\n        }\r\n      );\r\n    }\r\n  }, [isMenuOpen]);\r\n\r\n  useGSAP(() => {\r\n    handleMenuAnimation();\r\n  }, [isMenuOpen, handleMenuAnimation]);\r\n\r\n  const handleMenuClose = useCallback(() => {\r\n    gsap.to(menuRef.current, {\r\n      opacity: 0,\r\n      duration: 0.4,\r\n      ease: \"power2.inOut\",\r\n      onComplete: () => setIsMenuOpen(false)\r\n    });\r\n  }, []);\r\n\r\n  const toggleMenu = useCallback(() => {\r\n    setIsMenuOpen(prev => !prev);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex justify-between items-center p-4 fixed w-full top-0 z-50\">\r\n        <div className=\"text-2xl font-bold\">\r\n          <a href=\"/\">Portfolio</a>\r\n        </div>\r\n        <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />\r\n      </div>\r\n\r\n      {isMenuOpen && (\r\n        <div ref={menuRef} className=\"fixed inset-0 bg-white bg-opacity-95 z-40 flex items-center justify-center\">\r\n          <nav className=\"text-center\">\r\n            <ul className=\"flex flex-col gap-8\">\r\n              {MENU_ITEMS.map((item, index) => (\r\n                <MenuItem\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  label={item.label}\r\n                  ref={el => menuItemsRef.current[index] = el}\r\n                />\r\n              ))}\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAU,OAAO;IAAQ;IACjC;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAW,OAAO;IAAS;CACpC;AAED,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,iBACrC,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,SAAS,MAAM;;;;;;AAIpB,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,iBACpC,8OAAC;QAAG,KAAK;kBACP,cAAA,8OAAC;YAAE,WAAU;sBACX,cAAA,8OAAC,qJAAA,CAAA,UAAQ;gBAAC,MAAM;0BAAO;;;;;;;;;;;;;;;;AAKtB,MAAM,SAAS;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE9B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,YAAY;YACd,6IAAA,CAAA,UAAI,CAAC,MAAM,CAAC,QAAQ,OAAO,EACzB;gBAAE,SAAS;YAAE,GACb;gBAAE,SAAS;gBAAG,UAAU;gBAAK,MAAM;YAAe;YAGpD,6IAAA,CAAA,UAAI,CAAC,MAAM,CAAC,aAAa,OAAO,EAC9B;gBAAE,GAAG,CAAC;gBAAI,SAAS;YAAE,GACrB;gBACE,GAAG;gBACH,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,MAAM;YACR;QAEJ;IACF,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE;QACN;IACF,GAAG;QAAC;QAAY;KAAoB;IAEpC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,6IAAA,CAAA,UAAI,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE;YACvB,SAAS;YACT,UAAU;YACV,MAAM;YACN,YAAY,IAAM,cAAc;QAClC;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,cAAc,CAAA,OAAQ,CAAC;IACzB,GAAG,EAAE;IAEL,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;kCAEd,8OAAC;wBAAW,QAAQ;wBAAY,SAAS;;;;;;;;;;;;YAG1C,4BACC,8OAAC;gBAAI,KAAK;gBAAS,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,OAAO,KAAK,KAAK;gCACjB,KAAK,CAAA,KAAM,aAAa,OAAO,CAAC,MAAM,GAAG;+BAHpC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAYhC", "debugId": null}}]}