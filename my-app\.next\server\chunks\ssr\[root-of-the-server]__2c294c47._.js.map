{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\n// Dynamically import the water ripple effect to avoid SSR issues\r\nconst WaterRippleEffect = dynamic(\r\n  () => import(\"@/components/ui/WaterRippleEffect\"),\r\n  {\r\n    ssr: false,\r\n  }\r\n);\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden\">\r\n      {/* Water ripple effect background */}\r\n      <WaterRippleEffect />\r\n\r\n      <div className=\"content-overlay\">\r\n        <h1 className=\"text-black text-7xl flex items-center justify-center mt-100\">\r\n          I&apos;m\r\n          <Image\r\n            src=\"/profilePhoto.jpg\"\r\n            alt=\"Profile photo of Part<PERSON> Chauhan\"\r\n            width={80}\r\n            height={80}\r\n            className=\"w-20 h-15 rounded-xl mx-2 object-cover\"\r\n          />\r\n          Part<PERSON><PERSON>\r\n        </h1>\r\n        <h2 className=\"text-black text-7xl flex items-center justify-center mt-4 font-bold\">\r\n          Bringing Ideas to reality\r\n        </h2>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;;AALA;;;;AAOA,iEAAiE;AACjE,MAAM,oBAAoB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAG5B,KAAK;;AAIT,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;;;;0BAED,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA8D;0CAE1E,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;4BACV;;;;;;;kCAGJ,8OAAC;wBAAG,WAAU;kCAAsE;;;;;;;;;;;;;;;;;;AAM5F;uCAEe", "debugId": null}}]}