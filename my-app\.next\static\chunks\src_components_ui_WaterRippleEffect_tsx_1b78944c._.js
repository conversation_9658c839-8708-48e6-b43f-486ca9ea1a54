(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/WaterRippleEffect.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {

var { k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const e = new Error("Could not parse module '[project]/src/components/ui/WaterRippleEffect.tsx'");
e.code = 'MODULE_UNPARSABLE';
throw e;
}}),
"[project]/src/components/ui/WaterRippleEffect.tsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/ui/WaterRippleEffect.tsx [app-client] (ecmascript)"));
}),
}]);