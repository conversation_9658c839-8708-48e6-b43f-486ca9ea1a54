{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden\">\r\n      <h1 className=\"text-black text-7xl flex items-center justify-center mt-100\">\r\n        I&apos;m\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-20 h-15 rounded-xl  mx-2 object-cover\"\r\n        />\r\n        <PERSON><PERSON> Chauhan\r\n      </h1>\r\n      <h2 className=\"text-black text-7xl flex items-center justify-center mt-4 font-bold\">\r\n        Bringing Ideas to reality\r\n      </h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAOA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBAA8D;kCAE1E,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;oBACV;;;;;;;0BAGJ,8OAAC;gBAAG,WAAU;0BAAsE;;;;;;;;;;;;AAK1F;uCAEe", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState } from \"react\";\r\nimport FlipLink from \"./text-effect-flipper\";\r\n\r\nexport const Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex justify-between items-center p-4 bg-gray-800 text-white fixed w-full top-0 z-50\">\r\n        <div className=\"text-2xl font-bold\">\r\n          <a href=\"/\">Portfolio</a>\r\n        </div>\r\n        <button \r\n          onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n          className=\"text-4xl hover:text-gray-300 transition-colors\"\r\n          aria-label=\"Toggle menu\"\r\n        >\r\n          ☰\r\n        </button>\r\n      </div>\r\n\r\n      {/* Full-screen menu overlay */}\r\n      {isMenuOpen && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-95 z-40 flex items-center justify-center\">\r\n          <nav className=\"text-center\">\r\n            <button \r\n              onClick={() => setIsMenuOpen(false)}\r\n              className=\"absolute top-6 right-6 text-4xl text-white hover:text-gray-300\"\r\n            >\r\n              ×\r\n            </button>\r\n            <ul className=\"flex flex-col space-y-8\">\r\n              <li><a href=\"/\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">\r\n                \r\n              </a></li>\r\n              <li><a href=\"#about\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">About</a></li>\r\n              <li><a href=\"#projects\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Projects</a></li>\r\n              <li><a href=\"#skills\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Skills</a></li>\r\n              <li><a href=\"#contact\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Contact</a></li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIO,MAAM,SAAS;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;kCAEd,8OAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;wBACV,cAAW;kCACZ;;;;;;;;;;;;YAMF,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAI,WAAU;;;;;;;;;;;8CAG1B,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAmG;;;;;;;;;;;8CAClI,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAmG;;;;;;;;;;;8CACrI,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAU,WAAU;kDAAmG;;;;;;;;;;;8CACnI,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlJ", "debugId": null}}]}