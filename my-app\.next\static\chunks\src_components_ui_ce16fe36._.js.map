{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden\">\r\n      <h1 className=\"font-myfont text-black text-7xl flex items-center justify-center mt-100\">\r\n        I&apos;m\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-20 h-15 rounded-xl  mx-2 object-cover\"\r\n        />\r\n        <PERSON><PERSON> Chauhan\r\n      </h1>\r\n      <h2 className=\"text-black text-7xl flex items-center justify-center mt-4 font-bold font-myfont\">\r\n        Bringing Ideas to reality\r\n      </h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAOA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;oBAA0E;kCAEtF,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;oBACV;;;;;;;0BAGJ,6LAAC;gBAAG,WAAU;0BAAkF;;;;;;;;;;;;AAKtG;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\n\nconst DURATION = 0.30;\nconst STAGGER = 0.025;\n\ninterface FlipLinkProps {\n  children: string;\n  href: string;\n}\n\nconst FlipLink: React.FC<FlipLinkProps> = ({ children, href }) => {\n  return (\n    <motion.a\n      initial=\"initial\"\n      whileHover=\"hovered\"\n      target=\"_blank\"\n      href={href}\n      className=\"relative block overflow-hidden whitespace-nowrap text-4xl font-semibold uppercase dark:text-white/90 sm:text-7xl md:text-8xl \"\n      style={{\n        lineHeight: 0.75,\n      }}\n    >\n      <div>\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: 0,\n              },\n              hovered: {\n                y: \"-100%\",\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n      <div className=\"absolute inset-0\">\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: \"100%\",\n              },\n              hovered: {\n                y: 0,\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n    </motion.a>\n  );\n};\n\nexport default FlipLink;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,WAAW;AACjB,MAAM,UAAU;AAOhB,MAAM,WAAoC;QAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC3D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;QACP,SAAQ;QACR,YAAW;QACX,QAAO;QACP,MAAM;QACN,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,6LAAC;0BACE,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;0BAMX,6LAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;;;;;;;AAQjB;KA5DM;uCA8DS", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState, useRef, useCallback } from \"react\";\r\nimport FlipLink from \"./text-effect-flipper\";\r\nimport { useGSAP } from \"@gsap/react\";\r\nimport gsap from \"gsap\";\r\n\r\nconst MENU_ITEMS = [\r\n  { href: \"/\", label: \"Home\" },\r\n  { href: \"#about\", label: \"About\" },\r\n  { href: \"#projects\", label: \"Projects\" },\r\n  { href: \"#skills\", label: \"Skills\" }\r\n];\r\n\r\nconst MenuButton = ({ isOpen, onClick }) => (\r\n  <button \r\n    onClick={onClick}\r\n    className=\"text-4xl hover:text-gray-300 transition-colors\"\r\n    aria-label=\"Toggle menu\"\r\n  >\r\n    {isOpen ? '×' : '☰'}\r\n  </button>\r\n);\r\n\r\nconst MenuItem = ({ href, label, ref }) => (\r\n  <li ref={ref}>\r\n    <a className=\"text-4xl flex item-center hover:text-red-800 transition-colors transform inline-block ease-in\">\r\n      <FlipLink href={href}>{label}</FlipLink>\r\n    </a>\r\n  </li>\r\n);\r\n\r\nexport const Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const menuRef = useRef(null);\r\n  const menuItemsRef = useRef([]);\r\n\r\n  const handleMenuAnimation = useCallback(() => {\r\n    if (isMenuOpen) {\r\n      gsap.fromTo(menuRef.current,\r\n        { opacity: 0 },\r\n        { opacity: 1, duration: 0.5, ease: \"power2.inOut\" }\r\n      );\r\n\r\n      gsap.fromTo(menuItemsRef.current,\r\n        { y: -50, opacity: 0 },\r\n        {\r\n          y: 0,\r\n          opacity: 1,\r\n          duration: 0.6,\r\n          stagger: 0.1,\r\n          ease: \"back.out(1.7)\"\r\n        }\r\n      );\r\n    }\r\n  }, [isMenuOpen]);\r\n\r\n  useGSAP(() => {\r\n    handleMenuAnimation();\r\n  }, [isMenuOpen, handleMenuAnimation]);\r\n\r\n  const handleMenuClose = useCallback(() => {\r\n    gsap.to(menuRef.current, {\r\n      opacity: 0,\r\n      duration: 0.4,\r\n      ease: \"power2.inOut\",\r\n      onComplete: () => setIsMenuOpen(false)\r\n    });\r\n  }, []);\r\n\r\n  const toggleMenu = useCallback(() => {\r\n    setIsMenuOpen(prev => !prev);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex justify-between items-center p-4 fixed w-full top-0 z-50\">\r\n        <div className=\"text-2xl font-bold\">\r\n          <a href=\"/\">Portfolio</a>\r\n        </div>\r\n        <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />\r\n      </div>\r\n\r\n      {isMenuOpen && (\r\n        <div ref={menuRef} className=\"fixed inset-0 bg-white bg-opacity-95 z-40 flex items-center justify-center\">\r\n          <nav className=\"text-center\">\r\n            <ul className=\"flex flex-col gap-8\">\r\n              {MENU_ITEMS.map((item, index) => (\r\n                <MenuItem\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  label={item.label}\r\n                  ref={el => menuItemsRef.current[index] = el}\r\n                />\r\n              ))}\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;AAJA;;;;;AAMA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAU,OAAO;IAAQ;IACjC;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAW,OAAO;IAAS;CACpC;AAED,MAAM,aAAa;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE;yBACrC,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,SAAS,MAAM;;;;;;;KANd;AAUN,MAAM,WAAW;QAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;yBACpC,6LAAC;QAAG,KAAK;kBACP,cAAA,6LAAC;YAAE,WAAU;sBACX,cAAA,6LAAC,wJAAA,CAAA,UAAQ;gBAAC,MAAM;0BAAO;;;;;;;;;;;;;;;;;MAHvB;AAQC,MAAM,SAAS;;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE9B,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACtC,IAAI,YAAY;gBACd,gJAAA,CAAA,UAAI,CAAC,MAAM,CAAC,QAAQ,OAAO,EACzB;oBAAE,SAAS;gBAAE,GACb;oBAAE,SAAS;oBAAG,UAAU;oBAAK,MAAM;gBAAe;gBAGpD,gJAAA,CAAA,UAAI,CAAC,MAAM,CAAC,aAAa,OAAO,EAC9B;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE,GACrB;oBACE,GAAG;oBACH,SAAS;oBACT,UAAU;oBACV,SAAS;oBACT,MAAM;gBACR;YAEJ;QACF;kDAAG;QAAC;KAAW;IAEf,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;0BAAE;YACN;QACF;yBAAG;QAAC;QAAY;KAAoB;IAEpC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAClC,gJAAA,CAAA,UAAI,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE;gBACvB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;2DAAE,IAAM,cAAc;;YAClC;QACF;8CAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YAC7B;kDAAc,CAAA,OAAQ,CAAC;;QACzB;yCAAG,EAAE;IAEL,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;kCAEd,6LAAC;wBAAW,QAAQ;wBAAY,SAAS;;;;;;;;;;;;YAG1C,4BACC,6LAAC;gBAAI,KAAK;gBAAS,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,OAAO,KAAK,KAAK;gCACjB,KAAK,CAAA,KAAM,aAAa,OAAO,CAAC,MAAM,GAAG;+BAHpC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAYhC;GArEa;;QAyBX,kJAAA,CAAA,UAAO;;;MAzBI", "debugId": null}}]}