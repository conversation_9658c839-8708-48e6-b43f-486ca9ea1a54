{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/homePage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/homePage.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/homePage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/homePage.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\n\r\nexport const Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex justify-between items-center p-4 bg-gray-800 text-white fixed w-full top-0 z-50\">\r\n        <div className=\"text-2xl font-bold\">\r\n          <a href=\"/\">Portfolio</a>\r\n        </div>\r\n        <button \r\n          onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n          className=\"text-4xl hover:text-gray-300 transition-colors\"\r\n          aria-label=\"Toggle menu\"\r\n        >\r\n          ☰\r\n        </button>\r\n      </div>\r\n\r\n      {/* Full-screen menu overlay */}\r\n      {isMenuOpen && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-95 z-40 flex items-center justify-center\">\r\n          <nav className=\"text-center\">\r\n            <button \r\n              onClick={() => setIsMenuOpen(false)}\r\n              className=\"absolute top-6 right-6 text-4xl text-white hover:text-gray-300\"\r\n            >\r\n              ×\r\n            </button>\r\n            <ul className=\"flex flex-col space-y-8\">\r\n              <li><a href=\"/\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Home</a></li>\r\n              <li><a href=\"#about\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">About</a></li>\r\n              <li><a href=\"#projects\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Projects</a></li>\r\n              <li><a href=\"#skills\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Skills</a></li>\r\n              <li><a href=\"#contact\" className=\"text-4xl text-white hover:text-gray-300 transition-colors transform hover:scale-110 inline-block\">Contact</a></li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,MAAM,SAAS;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;kCAEd,8OAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;wBACV,cAAW;kCACZ;;;;;;;;;;;;YAMF,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAmG;;;;;;;;;;;8CAC7H,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAmG;;;;;;;;;;;8CAClI,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAmG;;;;;;;;;;;8CACrI,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAU,WAAU;kDAAmG;;;;;;;;;;;8CACnI,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlJ", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["import HomePage from \"@/components/ui/homePage\";\nimport { Navbar } from \"@/components/ui/navbar\";\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\nimport React from \"react\";\n\n\nexport default function page() {\n  return (\n    <div>\n      <Navbar />\n      <HomePage/>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,kIAAA,CAAA,SAAM;;;;;0BACP,8OAAC,oIAAA,CAAA,UAAQ;;;;;;;;;;;AAGf", "debugId": null}}]}